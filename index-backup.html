<!DOCTYPE html>
<html lang="en">

<head>
   <meta charset="UTF-8" />
   <meta name="viewport" content="width=device-width, initial-scale=1.0" />
   <title>CodeByEbra | Portfolio</title>
   <script src="https://cdn.tailwindcss.com"></script>
   <link rel="stylesheet" href="style.css">
   <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

   <link rel="preconnect" href="https://cdn.jsdelivr.net" />
   <link rel="preconnect" href="https://images.unsplash.com" />


<meta name="description"
   content="Portfolio of <PERSON><PERSON> (CodeByEbra), a full-stack web developer specializing in Python, Flask, JavaScript, and modern UI design." />
<meta name="keywords"
   content="Full-Stack Developer, Python, Flask, JavaScript, Tai<PERSON>wind, Portfolio, Web Design, Greece" />
<meta name="author" content="<PERSON><PERSON>" />

<!-- Open Graph (Facebook / LinkedIn / etc.) -->
<meta property="og:title" content="CodeByEbra | Full-Stack Developer Portfolio" />
<meta property="og:description" content="Explore my projects in Python, Flask, JavaScript, and responsive design." />
<meta property="og:url" content="https://codebyebra.pages.dev" />
<meta property="og:type" content="website" />

<!-- Twitter Card -->
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:title" content="CodeByEbra | Full-Stack Developer Portfolio" />
<meta name="twitter:description" content="Building modern, responsive, and user-friendly digital experiences." />

<!-- Canonical (avoid duplicate content issues) -->
<link rel="canonical" href="https://codebyebra.pages.dev" />

<!-- Robots -->
<meta name="robots" content="index, follow" />

<!-- Language -->
<meta http-equiv="Content-Language" content="en" />

</head>

<body class="font-sans bg-background text-text">

   <!-- Navbar -->
   <header class="fixed top-0 left-0 w-full z-50 bg-[var(--surface)]">
      <nav class="flex items-center justify-between max-w-6xl mx-auto p-4">
         <div class="text-xl font-bold nav-brand">CodeByEbra</div>

         <!-- Nav Links -->
         <ul id="nav-links" class="hidden md:flex space-x-6 font-medium">
            <li><a href="#about">About</a></li>
            <li><a href="#projects">Projects</a></li>
            <li><a href="#contact">Contact</a></li>
            <li><a href="https://linkedin.com/in/dimitrisebra" target="_blank"><i class="bi bi-linkedin"></i> LinkedIn</a></li>
            <li><a href="https://github.com/NError404F" target="_blank"><i class="bi bi-github"></i> GitHub</a></li>
         </ul>

         <!-- Hamburger Button -->
         <button id="menu-btn" class="md:hidden text-2xl focus:outline-none">
            <i id="menu-icon" class="bi bi-list"></i>
         </button>
         <ul id="mobile-nav"
            class="hidden absolute top-full left-0 w-full bg-[var(--surface)] flex-col items-center space-y-4 p-6 font-medium shadow-lg md:hidden">
            <li><a href="#about" class="block w-full text-center">About</a></li>
            <li><a href="#projects" class="block w-full text-center">Projects</a></li>
            <li><a href="#contact" class="block w-full text-center">Contact</a></li>
            <li><a href="https://linkedin.com/in/dimitrisebra" target="_blank"><i class="bi bi-linkedin"></i> LinkedIn</a></li>
            <li><a href="https://github.com/NError404F" target="_blank"><i class="bi bi-github"></i> GitHub</a></li>

         </ul>
         <div id="work-status" class="unavailable">
            <span id="status-dot" class="status-dot"></span>
            <span id="status-text">Checking...</span>
         </div>




      </nav>
   </header>


   <!-- Hero with gradient parallax -->
   <section class="hero flex items-center justify-center text-center text-text">
      <div class="max-w-2xl px-6">
         <h1 class="text-5xl md:text-7xl font-bold mb-6"  id="brand">CodeByEbra</h1>
         <p class="text-lg md:text-xl mb-8 italic" id="dev-quote"></p>
         <a href="#projects" class="inline-block px-6 py-3 border rounded view-work">View Work</a>
      </div>
   </section>

   <!-- About Me Section -->
   <section id="about" class="py-20 bg-gray-900 text-gray-200">
      <div class="max-w-5xl mx-auto px-6 text-center">
         <h2 class="section-title mb-8">About Me</h2>

         <p class="mb-6 text-lg leading-relaxed">
            I’m an <strong>18-year-old developer based in Greece</strong>, passionate about
            building modern, functional, and visually engaging digital experiences.
         </p>

         <p class="mb-6 text-lg leading-relaxed">
            Currently, I’m studying <strong>Full Stack Web Development, Software Development,
               Web Design, and Computer Science</strong>. My focus is on crafting
            clean, responsive, and user-friendly solutions that bring ideas to life —
            from concept to deployment.
         </p>

         <p class="mb-6 text-lg leading-relaxed">
            With a drive to learn and grow, I’m constantly exploring new technologies,
            frameworks, and design practices. Whether it’s developing robust backend
            systems, designing sleek frontends, or solving real-world problems with
            code, I’m dedicated to pushing myself forward and delivering value.
         </p>

         <p class="text-lg font-semibold text-accent1">
            My goal: <em>to turn creativity and logic into impactful digital solutions.</em>
         </p>
      </div>
   </section>

<!-- Active Tech Stack Section -->
<section id="tech" class="py-20 bg-gray-800 text-white">
   <div class="max-w-6xl mx-auto px-6 text-center">
      <h2 class="section-title mb-12">Active Tech Stack</h2>

      <!-- Active Tech Icons Grid -->
      <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-8">
         <div class="flex flex-col items-center tech-item">
            <i class="bi bi-filetype-html text-5xl text-orange-600"></i>
            <p class="mt-2 font-medium">HTML5</p>
         </div>
         <div class="flex flex-col items-center tech-item">
            <i class="bi bi-filetype-css text-5xl text-blue-600"></i>
            <p class="mt-2 font-medium">CSS3</p>
         </div>
         <div class="flex flex-col items-center tech-item">
            <i class="bi bi-filetype-js text-5xl text-yellow-500"></i>
            <p class="mt-2 font-medium">JavaScript</p>
         </div>
         <div class="flex flex-col items-center tech-item">
            <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/python/python-original.svg" alt="Python"
               class="w-12 h-12" />
            <p class="mt-2 font-medium">Python</p>
         </div>
         <div class="flex flex-col items-center tech-item">
            <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/flask/flask-original.svg" alt="Flask"
               class="w-12 h-12" />
            <p class="mt-2 font-medium">Flask</p>
         </div>
         <div class="flex flex-col items-center tech-item">
            <i class="bi bi-layers text-5xl text-cyan-600"></i>
            <p class="mt-2 font-medium">Tailwind</p>
         </div>
         <div class="flex flex-col items-center tech-item">
            <i class="bi bi-bootstrap text-5xl text-purple-600"></i>
            <p class="mt-2 font-medium">Bootstrap</p>
         </div>
         <div class="flex flex-col items-center tech-item">
            <i class="bi bi-database text-5xl text-green-600"></i>
            <p class="mt-2 font-medium">MySQL</p>
         </div>
      <div class="flex flex-col items-center tech-item">
         <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/cplusplus/cplusplus-original.svg" alt="C++"
            class="w-12 h-12" />
         <p class="mt-2 font-medium">C++</p>
      </div>
      <div class="flex flex-col items-center tech-item">
         <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/arduino/arduino-original.svg" alt="Arduino"
            class="w-12 h-12" />
         <p class="mt-2 font-medium">Arduino</p>
      </div>
      <div class="flex flex-col items-center tech-item">
         <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/python/python-original.svg" alt="MicroPython"
            class="w-12 h-12" />
         <p class="mt-2 font-medium">MicroPython</p>
      </div>
      <div class="flex flex-col items-center tech-item">
         <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/raspberrypi/raspberrypi-original.svg" alt="Raspberry Pi"
            class="w-12 h-12" />
         <p class="mt-2 font-medium">Raspberry Pi</p>
      </div>
      </div>
   </div>
</section>

<!-- Past Experience Section -->
<section id="past-tech" class="py-20 bg-gray-900 text-gray-200">
   <div class="max-w-6xl mx-auto px-6 text-center">
      <h2 class="section-title mb-12">Previously Worked With</h2>

      <!-- Past Tech Icons Grid -->
      <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-8">
      <div class="flex flex-col items-center tech-item">
         <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/csharp/csharp-original.svg" alt="C#"
            class="w-12 h-12" />
         <p class="mt-2 font-medium">C#</p>
      </div>
      <div class="flex flex-col items-center tech-item">
         <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/lua/lua-original.svg" alt="Lua" class="w-12 h-12" />
         <p class="mt-2 font-medium">Lua</p>
      </div>
         <!-- Add more if needed -->
      </div>
   </div>
</section>



   <!-- Projects Section -->
   <section id="projects" class="py-20">
      <div class="max-w-6xl mx-auto px-6">
         <h2 class="section-title">Projects</h2>

         <!-- Projects Container -->
         <div id="projects-container" class="space-y-12">
            <!-- Projects will be dynamically rendered here -->
                  <strong>elmylonas.gr</strong> is the official digital hub of
                  Λευτέρης Μυλωνάς, an economist and secondary-education teacher.
                  The website was built as a <em>complete professional presence</em>
                  with both public-facing content for students and institutions, and
                  a private administration environment for content management.
               </p>

               <p class="mb-6">
                  The platform combines a <strong>modern, responsive frontend</strong>
                  with a <strong>secure backend and admin panel</strong>. The
                  administrator can log in, upload and update teaching material,
                  manage exam exercises, and publish announcements without touching
                  code. This transforms the site from a static portfolio into a
                  living resource that evolves with the educator’s work.
               </p>

               <p class="mb-6">
                  On the frontend, visitors can easily explore academic background,
                  downloadable teaching resources, and tutoring information, with a
                  design that emphasizes clarity, trust, and accessibility. Integrated
                  contact options (email, phone, social links) and remote learning
                  support (Zoom) ensure seamless communication with students.
               </p>

               <h2 class="mb-6 text-2xl">Lessons from My First Freelance Project</h2>

               <p class="mb-6">
                  My first freelancing opportunity came from my economics teacher, who trusted me to build and maintain a web application
                  for their needs. It was a real turning point for me, because I had to approach the project as both a developer and a
                  service provider. I learned how to communicate with a client, gather requirements, meet deadlines, and deliver revisions
                  without losing sight of the bigger picture. On the technical side, I combined Python (Flask) with JavaScript to create a
                  responsive full-stack solution, implemented secure user authentication, and protected the app against common
                  vulnerabilities like XSS and SQL injection. I also gained valuable experience managing databases carefully during major
                  updates, ensuring no data was lost, and updating the system without breaking existing functionality. Beyond development,
                  I took responsibility for hosting and deploying the Flask application, learned how to monitor and maintain it in
                  production, and still ensure that the client’s system remains stable. This project not only sharpened my technical
                  skills in building professional CRUD applications but also taught me the importance of reliability, security, and clear
                  communication when delivering real-world solutions.
               </p>

               <!-- Testimonial Card -->
               <div class="bg-gray-800 text-gray-200 p-6 rounded-xl shadow-md mb-6">
                  <p id="review-gr" class="italic">
                     “Άψογη δουλειά, επαγγελματισμός, ακριβώς όπως ζήτησα το site,
                     tailor made, on time και εξαιρετικό after sales service.”
                  </p>
                  <p id="review-en" class="italic hidden">
                     “Excellent work, professionalism, exactly as I requested the site,
                     tailor made, on time and outstanding after-sales service.”
                  </p>

                  <!-- Stars -->
                  <div class="mt-4 flex items-center gap-1 text-yellow-400">
                     <i class="bi bi-star-fill"></i>
                     <i class="bi bi-star-fill"></i>
                     <i class="bi bi-star-fill"></i>
                     <i class="bi bi-star-fill"></i>
                     <i class="bi bi-star-fill"></i>
                  </div>

                  <!-- Translate button -->
                  <button
                     onclick="document.getElementById('review-gr').classList.toggle('hidden'); document.getElementById('review-en').classList.toggle('hidden');"
                     class="mt-4 flex items-center gap-2 text-accent1 hover:underline">
                     <i class="bi bi-translate"></i> Translate
                  </button>
               </div>

               <div class="flex gap-3">
                  <a href="https://elmylonas.gr" target="_blank" class="btn">View Live Site</a>
               </div>
            </div>
         </div>
      </div>
   </section>






   <section id="contact" class="py-20 max-w-6xl mx-auto px-6">
      <h2 class="section-title">Contact</h2>
      <form class="max-w-xl mx-auto space-y-6" action="https://formspree.io/f/xkgvdego" method="POST">

         <!-- Name -->
         <input type="text" name="name" placeholder="Name" class="input" required>

         <!-- Email -->
         <input type="email" name="email" placeholder="Email" class="input" required>

         <!-- Subject -->
         <input type="text" name="subject" placeholder="Subject" class="input">

         <!-- Message -->
         <textarea name="message" placeholder="Message" class="input h-32" required></textarea>

         <!-- Submit -->
         <button type="submit" class="btn">Send Message</button>
      </form>
   </section>


   <!-- Footer -->
<footer class="py-8 text-center border-t border-border">
   <p>&copy; 2025 Dimitris Ebrahim. All rights reserved.</p>
   <p>Made with 💖</p>
<p>
   <a href="https://linkedin.com/in/dimitrisebra" target="_blank" rel="noopener noreferrer"
      class="text-decoration-none text-primary d-inline-flex align-items-center gap-2">
      <i class="bi bi-linkedin"></i>
   </a>
   <a href="https://github.com/NError404F" target="_blank" rel="noopener noreferrer"
      class="text-decoration-none text-primary d-inline-flex align-items-center gap-2">
      <i class="bi bi-github"></i>
   </a>
</p>
</footer>


   <!-- Go to Top Button -->
   <button id="goTopBtn" class="hidden fixed bottom-6 right-6 p-3 ">
      <i class="bi bi-arrow-up"></i>
   </button>

   <script src="script.js"></script>
   <script type="application/ld+json">
   {
     "@context": "https://schema.org",
     "@type": "Person",
     "name": "Dimitris Ebrahim",
     "url": "https://codebyebra.pages.dev",
     "sameAs": [
       "https://github.com/NError404F"
     ],
     "jobTitle": "Full-Stack Developer",
     "worksFor": {
       "@type": "Organization",
       "name": "Freelance"
     },
  "description": "Full-Stack Developer specializing in Python, Flask, and modern front-end frameworks."
}
   </script>
</body>

</html>